-- إدراج البيانات الافتراضية للنظام

-- إنشاء المستخدم الرئيسي (كلمة المرور: admin123)
-- تم تشفير كلمة المرور باستخدام SHA-256
INSERT OR IGNORE INTO users (username, password_hash, role, full_name, email, is_active) VALUES 
('admin', 'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f', 'ADMIN', 'المدير العام', '<EMAIL>', 1);

-- إنشاء فترة مالية افتراضية للعام الحالي
INSERT OR IGNORE INTO periods (name, start_date, end_date, is_closed) VALUES 
('السنة المالية 2024', '2024-01-01', '2024-12-31', 0);

-- إعدادات الشركة الافتراضية
INSERT OR IGNORE INTO settings (company_name, company_info, company_address, company_phone, company_email, fiscal_year_start, currency) VALUES 
('شركة المحاسبة المتكاملة', 'نظام محاسبي متكامل', 'العنوان الرئيسي للشركة', '+966-XX-XXXXXXX', '<EMAIL>', '01-01', 'SAR');

-- إنشاء أرصدة افتتاحية للحسابات الرئيسية
INSERT OR IGNORE INTO account_balances (account_id, period_id, opening_balance, closing_balance, total_debit, total_credit)
SELECT 
    a.id,
    p.id,
    a.opening_balance,
    a.opening_balance,
    0,
    0
FROM accounts a
CROSS JOIN periods p
WHERE p.name = 'السنة المالية 2024';
