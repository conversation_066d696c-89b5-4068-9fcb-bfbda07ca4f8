// أنواع البيانات للنظام المحاسبي

export type AccountType = 'ASSET' | 'LIABILITY' | 'EQUITY' | 'REVENUE' | 'EXPENSE';

export interface Account {
  id: number;
  code: string;
  name: string;
  parent_id?: number;
  account_type: AccountType;
  is_active: boolean;
  opening_balance: number;
  created_at: string;
  updated_at: string;
}

export interface Period {
  id: number;
  name: string;
  start_date: string;
  end_date: string;
  is_closed: boolean;
  created_at: string;
  updated_at: string;
}

export interface JournalEntry {
  id: number;
  entry_number: string;
  date: string;
  description: string;
  period_id: number;
  created_by: number;
  is_posted: boolean;
  total_debit: number;
  total_credit: number;
  created_at: string;
  updated_at: string;
}

export interface JournalLine {
  id: number;
  entry_id: number;
  account_id: number;
  debit: number;
  credit: number;
  description: string;
  line_number: number;
  created_at: string;
}

export interface User {
  id: number;
  username: string;
  role: 'ADMIN' | 'USER' | 'VIEWER';
  full_name: string;
  email: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Settings {
  id: number;
  company_name: string;
  company_logo?: string;
  company_info?: string;
  company_address?: string;
  company_phone?: string;
  company_email?: string;
  fiscal_year_start: string;
  currency: string;
  created_at: string;
  updated_at: string;
}

export interface TrialBalanceItem {
  account_id: number;
  account_code: string;
  account_name: string;
  opening_debit: number;
  opening_credit: number;
  period_debit: number;
  period_credit: number;
  closing_debit: number;
  closing_credit: number;
}

export interface IncomeStatementItem {
  account_id: number;
  account_code: string;
  account_name: string;
  amount: number;
  account_type: AccountType;
}

export interface BalanceSheetItem {
  account_id: number;
  account_code: string;
  account_name: string;
  amount: number;
  account_type: AccountType;
}

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  status: number;
}

export interface CreateJournalEntryRequest {
  date: string;
  description: string;
  period_id: number;
  lines: {
    account_id: number;
    debit: number;
    credit: number;
    description: string;
  }[];
}

export interface UpdateJournalEntryRequest extends CreateJournalEntryRequest {
  id: number;
}
