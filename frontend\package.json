{"name": "accounting-system-frontend", "version": "1.0.0", "description": "نظام محاسبي متكامل - واجهة المستخدم", "private": true, "dependencies": {"@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "typescript": "^4.9.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "chart.js": "^4.2.0", "react-chartjs-2": "^5.2.0", "jspdf": "^2.5.0", "xlsx": "^0.18.0", "axios": "^1.3.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "date-fns": "^2.29.0", "react-hook-form": "^7.43.0", "react-hot-toast": "^2.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jspdf": "^2.3.0"}, "proxy": "http://localhost:8080"}