#include "db.h"
#include <stdio.h>

int db_open(const char *filename, sqlite3 **db) {
    return sqlite3_open(filename, db);
}

void db_close(sqlite3 *db) {
    if (db) sqlite3_close(db);
}

int db_init(sqlite3 *db) {
    // تنفيذ ملف schema.sql لتهيئة الجداول
    FILE *f = fopen("schema.sql", "r");
    if (!f) return -1;
    fseek(f, 0, SEEK_END);
    long len = ftell(f);
    fseek(f, 0, SEEK_SET);
    char *sql = malloc(len + 1);
    fread(sql, 1, len, f);
    sql[len] = '\0';
    fclose(f);
    char *errmsg = 0;
    int rc = sqlite3_exec(db, sql, 0, 0, &errmsg);
    free(sql);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "DB init error: %s\n", errmsg);
        sqlite3_free(errmsg);
        return rc;
    }
    return 0;
} 