#include "db.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

// دوال قاعدة البيانات الأساسية
int db_open(const char *filename, sqlite3 **db) {
    return sqlite3_open(filename, db);
}

void db_close(sqlite3 *db) {
    if (db) sqlite3_close(db);
}

int db_init(sqlite3 *db) {
    // تنفيذ ملف schema.sql لتهيئة الجداول
    FILE *f = fopen("schema.sql", "r");
    if (!f) return -1;
    fseek(f, 0, SEEK_END);
    long len = ftell(f);
    fseek(f, 0, SEEK_SET);
    char *sql = malloc(len + 1);
    fread(sql, 1, len, f);
    sql[len] = '\0';
    fclose(f);
    char *errmsg = 0;
    int rc = sqlite3_exec(db, sql, 0, 0, &errmsg);
    free(sql);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "DB init error: %s\n", errmsg);
        sqlite3_free(errmsg);
        return rc;
    }

    // تحميل شجرة الحسابات الافتراضية
    rc = db_load_default_accounts(db);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Failed to load default accounts\n");
        return rc;
    }

    return 0;
}

int db_load_default_accounts(sqlite3 *db) {
    // التحقق من وجود حسابات مسبقاً
    const char *check_sql = "SELECT COUNT(*) FROM accounts";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, check_sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    rc = sqlite3_step(stmt);
    int count = 0;
    if (rc == SQLITE_ROW) {
        count = sqlite3_column_int(stmt, 0);
    }
    sqlite3_finalize(stmt);

    // إذا كانت هناك حسابات موجودة، لا نحمل الحسابات الافتراضية
    if (count > 0) return SQLITE_OK;

    // تحميل شجرة الحسابات من ملف accounts_data.sql
    FILE *f = fopen("accounts_data.sql", "r");
    if (!f) return -1;
    fseek(f, 0, SEEK_END);
    long len = ftell(f);
    fseek(f, 0, SEEK_SET);
    char *sql = malloc(len + 1);
    fread(sql, 1, len, f);
    sql[len] = '\0';
    fclose(f);

    char *errmsg = 0;
    rc = sqlite3_exec(db, sql, 0, 0, &errmsg);
    free(sql);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Failed to load accounts: %s\n", errmsg);
        sqlite3_free(errmsg);
        return rc;
    }

    printf("تم تحميل شجرة الحسابات بنجاح\n");
    return SQLITE_OK;
}

// دوال مساعدة
static void get_current_timestamp(char *buffer, size_t size) {
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}

static const char* account_type_to_string(AccountType type) {
    switch (type) {
        case ACCOUNT_TYPE_ASSET: return "ASSET";
        case ACCOUNT_TYPE_LIABILITY: return "LIABILITY";
        case ACCOUNT_TYPE_EQUITY: return "EQUITY";
        case ACCOUNT_TYPE_REVENUE: return "REVENUE";
        case ACCOUNT_TYPE_EXPENSE: return "EXPENSE";
        default: return "ASSET";
    }
}

static AccountType string_to_account_type(const char *str) {
    if (strcmp(str, "LIABILITY") == 0) return ACCOUNT_TYPE_LIABILITY;
    if (strcmp(str, "EQUITY") == 0) return ACCOUNT_TYPE_EQUITY;
    if (strcmp(str, "REVENUE") == 0) return ACCOUNT_TYPE_REVENUE;
    if (strcmp(str, "EXPENSE") == 0) return ACCOUNT_TYPE_EXPENSE;
    return ACCOUNT_TYPE_ASSET;
}

static const char* user_role_to_string(UserRole role) {
    switch (role) {
        case USER_ROLE_ADMIN: return "ADMIN";
        case USER_ROLE_USER: return "USER";
        case USER_ROLE_VIEWER: return "VIEWER";
        default: return "USER";
    }
}

static UserRole string_to_user_role(const char *str) {
    if (strcmp(str, "ADMIN") == 0) return USER_ROLE_ADMIN;
    if (strcmp(str, "VIEWER") == 0) return USER_ROLE_VIEWER;
    return USER_ROLE_USER;
}

// دوال إدارة الحسابات
int db_create_account(sqlite3 *db, const Account *account) {
    const char *sql = "INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    char timestamp[32];
    get_current_timestamp(timestamp, sizeof(timestamp));

    sqlite3_bind_text(stmt, 1, account->code, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, account->name, -1, SQLITE_STATIC);
    if (account->parent_id > 0) {
        sqlite3_bind_int(stmt, 3, account->parent_id);
    } else {
        sqlite3_bind_null(stmt, 3);
    }
    sqlite3_bind_text(stmt, 4, account_type_to_string(account->account_type), -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 5, account->is_active);
    sqlite3_bind_double(stmt, 6, account->opening_balance);
    sqlite3_bind_text(stmt, 7, timestamp, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 8, timestamp, -1, SQLITE_STATIC);

    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);
    return (rc == SQLITE_DONE) ? SQLITE_OK : rc;
}

int db_get_account(sqlite3 *db, int id, Account *account) {
    const char *sql = "SELECT id, code, name, parent_id, account_type, is_active, opening_balance, created_at, updated_at FROM accounts WHERE id = ?";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    sqlite3_bind_int(stmt, 1, id);
    rc = sqlite3_step(stmt);

    if (rc == SQLITE_ROW) {
        account->id = sqlite3_column_int(stmt, 0);
        strcpy(account->code, (char*)sqlite3_column_text(stmt, 1));
        strcpy(account->name, (char*)sqlite3_column_text(stmt, 2));
        account->parent_id = sqlite3_column_int(stmt, 3);
        account->account_type = string_to_account_type((char*)sqlite3_column_text(stmt, 4));
        account->is_active = sqlite3_column_int(stmt, 5);
        account->opening_balance = sqlite3_column_double(stmt, 6);
        strcpy(account->created_at, (char*)sqlite3_column_text(stmt, 7));
        strcpy(account->updated_at, (char*)sqlite3_column_text(stmt, 8));
        rc = SQLITE_OK;
    }

    sqlite3_finalize(stmt);
    return rc;
}

int db_get_account_by_code(sqlite3 *db, const char *code, Account *account) {
    const char *sql = "SELECT id, code, name, parent_id, account_type, is_active, opening_balance, created_at, updated_at FROM accounts WHERE code = ?";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    sqlite3_bind_text(stmt, 1, code, -1, SQLITE_STATIC);
    rc = sqlite3_step(stmt);

    if (rc == SQLITE_ROW) {
        account->id = sqlite3_column_int(stmt, 0);
        strcpy(account->code, (char*)sqlite3_column_text(stmt, 1));
        strcpy(account->name, (char*)sqlite3_column_text(stmt, 2));
        account->parent_id = sqlite3_column_int(stmt, 3);
        account->account_type = string_to_account_type((char*)sqlite3_column_text(stmt, 4));
        account->is_active = sqlite3_column_int(stmt, 5);
        account->opening_balance = sqlite3_column_double(stmt, 6);
        strcpy(account->created_at, (char*)sqlite3_column_text(stmt, 7));
        strcpy(account->updated_at, (char*)sqlite3_column_text(stmt, 8));
        rc = SQLITE_OK;
    }

    sqlite3_finalize(stmt);
    return rc;
}

int db_update_account(sqlite3 *db, const Account *account) {
    const char *sql = "UPDATE accounts SET code = ?, name = ?, parent_id = ?, account_type = ?, is_active = ?, opening_balance = ?, updated_at = ? WHERE id = ?";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    char timestamp[32];
    get_current_timestamp(timestamp, sizeof(timestamp));

    sqlite3_bind_text(stmt, 1, account->code, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, account->name, -1, SQLITE_STATIC);
    if (account->parent_id > 0) {
        sqlite3_bind_int(stmt, 3, account->parent_id);
    } else {
        sqlite3_bind_null(stmt, 3);
    }
    sqlite3_bind_text(stmt, 4, account_type_to_string(account->account_type), -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 5, account->is_active);
    sqlite3_bind_double(stmt, 6, account->opening_balance);
    sqlite3_bind_text(stmt, 7, timestamp, -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 8, account->id);

    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);
    return (rc == SQLITE_DONE) ? SQLITE_OK : rc;
}

int db_delete_account(sqlite3 *db, int id) {
    const char *sql = "DELETE FROM accounts WHERE id = ?";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    sqlite3_bind_int(stmt, 1, id);
    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);
    return (rc == SQLITE_DONE) ? SQLITE_OK : rc;
}

int db_get_accounts(sqlite3 *db, Account **accounts, int *count) {
    const char *sql = "SELECT id, code, name, parent_id, account_type, is_active, opening_balance, created_at, updated_at FROM accounts ORDER BY code";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    // حساب عدد الحسابات أولاً
    *count = 0;
    while (sqlite3_step(stmt) == SQLITE_ROW) {
        (*count)++;
    }
    sqlite3_finalize(stmt);

    if (*count == 0) {
        *accounts = NULL;
        return SQLITE_OK;
    }

    // تخصيص الذاكرة
    *accounts = malloc(sizeof(Account) * (*count));

    // إعادة تنفيذ الاستعلام لجلب البيانات
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        free(*accounts);
        return rc;
    }

    int i = 0;
    while (sqlite3_step(stmt) == SQLITE_ROW && i < *count) {
        (*accounts)[i].id = sqlite3_column_int(stmt, 0);
        strcpy((*accounts)[i].code, (char*)sqlite3_column_text(stmt, 1));
        strcpy((*accounts)[i].name, (char*)sqlite3_column_text(stmt, 2));
        (*accounts)[i].parent_id = sqlite3_column_int(stmt, 3);
        (*accounts)[i].account_type = string_to_account_type((char*)sqlite3_column_text(stmt, 4));
        (*accounts)[i].is_active = sqlite3_column_int(stmt, 5);
        (*accounts)[i].opening_balance = sqlite3_column_double(stmt, 6);
        strcpy((*accounts)[i].created_at, (char*)sqlite3_column_text(stmt, 7));
        strcpy((*accounts)[i].updated_at, (char*)sqlite3_column_text(stmt, 8));
        i++;
    }

    sqlite3_finalize(stmt);
    return SQLITE_OK;
}

// دوال إدارة قيود اليومية
int db_create_journal_entry(sqlite3 *db, const JournalEntry *entry) {
    const char *sql = "INSERT INTO journal_entries (entry_number, date, description, period_id, created_by, is_posted, total_debit, total_credit, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    char timestamp[32];
    get_current_timestamp(timestamp, sizeof(timestamp));

    sqlite3_bind_text(stmt, 1, entry->entry_number, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, entry->date, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 3, entry->description, -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 4, entry->period_id);
    sqlite3_bind_int(stmt, 5, entry->created_by);
    sqlite3_bind_int(stmt, 6, entry->is_posted);
    sqlite3_bind_double(stmt, 7, entry->total_debit);
    sqlite3_bind_double(stmt, 8, entry->total_credit);
    sqlite3_bind_text(stmt, 9, timestamp, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 10, timestamp, -1, SQLITE_STATIC);

    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);
    return (rc == SQLITE_DONE) ? SQLITE_OK : rc;
}

int db_get_journal_entry(sqlite3 *db, int id, JournalEntry *entry) {
    const char *sql = "SELECT id, entry_number, date, description, period_id, created_by, is_posted, total_debit, total_credit, created_at, updated_at FROM journal_entries WHERE id = ?";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    sqlite3_bind_int(stmt, 1, id);
    rc = sqlite3_step(stmt);

    if (rc == SQLITE_ROW) {
        entry->id = sqlite3_column_int(stmt, 0);
        strcpy(entry->entry_number, (char*)sqlite3_column_text(stmt, 1));
        strcpy(entry->date, (char*)sqlite3_column_text(stmt, 2));
        strcpy(entry->description, (char*)sqlite3_column_text(stmt, 3));
        entry->period_id = sqlite3_column_int(stmt, 4);
        entry->created_by = sqlite3_column_int(stmt, 5);
        entry->is_posted = sqlite3_column_int(stmt, 6);
        entry->total_debit = sqlite3_column_double(stmt, 7);
        entry->total_credit = sqlite3_column_double(stmt, 8);
        strcpy(entry->created_at, (char*)sqlite3_column_text(stmt, 9));
        strcpy(entry->updated_at, (char*)sqlite3_column_text(stmt, 10));
        rc = SQLITE_OK;
    }

    sqlite3_finalize(stmt);
    return rc;
}

int db_get_journal_entries(sqlite3 *db, int period_id, JournalEntry **entries, int *count) {
    const char *sql = period_id > 0 ?
        "SELECT id, entry_number, date, description, period_id, created_by, is_posted, total_debit, total_credit, created_at, updated_at FROM journal_entries WHERE period_id = ? ORDER BY date, entry_number" :
        "SELECT id, entry_number, date, description, period_id, created_by, is_posted, total_debit, total_credit, created_at, updated_at FROM journal_entries ORDER BY date, entry_number";

    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    if (period_id > 0) {
        sqlite3_bind_int(stmt, 1, period_id);
    }

    // حساب عدد القيود أولاً
    *count = 0;
    while (sqlite3_step(stmt) == SQLITE_ROW) {
        (*count)++;
    }
    sqlite3_finalize(stmt);

    if (*count == 0) {
        *entries = NULL;
        return SQLITE_OK;
    }

    // تخصيص الذاكرة
    *entries = malloc(sizeof(JournalEntry) * (*count));

    // إعادة تنفيذ الاستعلام
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        free(*entries);
        return rc;
    }

    if (period_id > 0) {
        sqlite3_bind_int(stmt, 1, period_id);
    }

    int i = 0;
    while (sqlite3_step(stmt) == SQLITE_ROW && i < *count) {
        (*entries)[i].id = sqlite3_column_int(stmt, 0);
        strcpy((*entries)[i].entry_number, (char*)sqlite3_column_text(stmt, 1));
        strcpy((*entries)[i].date, (char*)sqlite3_column_text(stmt, 2));
        strcpy((*entries)[i].description, (char*)sqlite3_column_text(stmt, 3));
        (*entries)[i].period_id = sqlite3_column_int(stmt, 4);
        (*entries)[i].created_by = sqlite3_column_int(stmt, 5);
        (*entries)[i].is_posted = sqlite3_column_int(stmt, 6);
        (*entries)[i].total_debit = sqlite3_column_double(stmt, 7);
        (*entries)[i].total_credit = sqlite3_column_double(stmt, 8);
        strcpy((*entries)[i].created_at, (char*)sqlite3_column_text(stmt, 9));
        strcpy((*entries)[i].updated_at, (char*)sqlite3_column_text(stmt, 10));
        i++;
    }

    sqlite3_finalize(stmt);
    return SQLITE_OK;
}

int db_delete_journal_entry(sqlite3 *db, int id) {
    const char *sql = "DELETE FROM journal_entries WHERE id = ?";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    sqlite3_bind_int(stmt, 1, id);
    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);
    return (rc == SQLITE_DONE) ? SQLITE_OK : rc;
}

// دوال إدارة تفاصيل القيود
int db_create_journal_line(sqlite3 *db, const JournalLine *line) {
    const char *sql = "INSERT INTO journal_lines (entry_id, account_id, debit, credit, description, line_number, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    char timestamp[32];
    get_current_timestamp(timestamp, sizeof(timestamp));

    sqlite3_bind_int(stmt, 1, line->entry_id);
    sqlite3_bind_int(stmt, 2, line->account_id);
    sqlite3_bind_double(stmt, 3, line->debit);
    sqlite3_bind_double(stmt, 4, line->credit);
    sqlite3_bind_text(stmt, 5, line->description, -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 6, line->line_number);
    sqlite3_bind_text(stmt, 7, timestamp, -1, SQLITE_STATIC);

    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);
    return (rc == SQLITE_DONE) ? SQLITE_OK : rc;
}

int db_get_journal_lines(sqlite3 *db, int entry_id, JournalLine **lines, int *count) {
    const char *sql = "SELECT id, entry_id, account_id, debit, credit, description, line_number, created_at FROM journal_lines WHERE entry_id = ? ORDER BY line_number";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    sqlite3_bind_int(stmt, 1, entry_id);

    // حساب عدد السطور أولاً
    *count = 0;
    while (sqlite3_step(stmt) == SQLITE_ROW) {
        (*count)++;
    }
    sqlite3_finalize(stmt);

    if (*count == 0) {
        *lines = NULL;
        return SQLITE_OK;
    }

    // تخصيص الذاكرة
    *lines = malloc(sizeof(JournalLine) * (*count));

    // إعادة تنفيذ الاستعلام
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        free(*lines);
        return rc;
    }

    sqlite3_bind_int(stmt, 1, entry_id);

    int i = 0;
    while (sqlite3_step(stmt) == SQLITE_ROW && i < *count) {
        (*lines)[i].id = sqlite3_column_int(stmt, 0);
        (*lines)[i].entry_id = sqlite3_column_int(stmt, 1);
        (*lines)[i].account_id = sqlite3_column_int(stmt, 2);
        (*lines)[i].debit = sqlite3_column_double(stmt, 3);
        (*lines)[i].credit = sqlite3_column_double(stmt, 4);
        strcpy((*lines)[i].description, (char*)sqlite3_column_text(stmt, 5));
        (*lines)[i].line_number = sqlite3_column_int(stmt, 6);
        strcpy((*lines)[i].created_at, (char*)sqlite3_column_text(stmt, 7));
        i++;
    }

    sqlite3_finalize(stmt);
    return SQLITE_OK;
}

int db_update_journal_line(sqlite3 *db, const JournalLine *line) {
    const char *sql = "UPDATE journal_lines SET account_id = ?, debit = ?, credit = ?, description = ?, line_number = ? WHERE id = ?";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    sqlite3_bind_int(stmt, 1, line->account_id);
    sqlite3_bind_double(stmt, 2, line->debit);
    sqlite3_bind_double(stmt, 3, line->credit);
    sqlite3_bind_text(stmt, 4, line->description, -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 5, line->line_number);
    sqlite3_bind_int(stmt, 6, line->id);

    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);
    return (rc == SQLITE_DONE) ? SQLITE_OK : rc;
}

int db_delete_journal_line(sqlite3 *db, int id) {
    const char *sql = "DELETE FROM journal_lines WHERE id = ?";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    sqlite3_bind_int(stmt, 1, id);
    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);
    return (rc == SQLITE_DONE) ? SQLITE_OK : rc;
}

int db_delete_journal_lines_by_entry(sqlite3 *db, int entry_id) {
    const char *sql = "DELETE FROM journal_lines WHERE entry_id = ?";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    sqlite3_bind_int(stmt, 1, entry_id);
    rc = sqlite3_step(stmt);
    sqlite3_finalize(stmt);
    return (rc == SQLITE_DONE) ? SQLITE_OK : rc;
}

// دالة للتحقق من توازن القيد
int db_validate_journal_entry(sqlite3 *db, int entry_id) {
    const char *sql = "SELECT SUM(debit), SUM(credit) FROM journal_lines WHERE entry_id = ?";
    sqlite3_stmt *stmt;
    int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) return rc;

    sqlite3_bind_int(stmt, 1, entry_id);
    rc = sqlite3_step(stmt);

    if (rc == SQLITE_ROW) {
        double total_debit = sqlite3_column_double(stmt, 0);
        double total_credit = sqlite3_column_double(stmt, 1);
        sqlite3_finalize(stmt);

        // التحقق من التوازن (مع هامش خطأ صغير للأرقام العشرية)
        if (fabs(total_debit - total_credit) < 0.01) {
            return SQLITE_OK;
        } else {
            return SQLITE_ERROR;
        }
    }

    sqlite3_finalize(stmt);
    return SQLITE_ERROR;
}