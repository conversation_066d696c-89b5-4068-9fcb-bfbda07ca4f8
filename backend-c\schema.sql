-- جدول الحسابات
CREATE TABLE accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL,
    parent_id INTEGER,
    account_type TEXT NOT NULL CHECK(account_type IN ('ASSET', 'LIA<PERSON>LITY', 'EQUITY', 'REVENUE', 'EXPENSE')),
    is_active INTEGER DEFAULT 1,
    opening_balance REAL DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(parent_id) REFERENCES accounts(id)
);

-- جدول الفترات المالية
CREATE TABLE periods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    start_date TEXT NOT NULL,
    end_date TEXT NOT NULL,
    is_closed INTEGER DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- جد<PERSON><PERSON> قيود اليومية
CREATE TABLE journal_entries (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    entry_number TEXT NOT NULL UNIQUE,
    date TEXT NOT NULL,
    description TEXT,
    period_id INTEGER NOT NULL,
    created_by INTEGER,
    is_posted INTEGER DEFAULT 0,
    total_debit REAL DEFAULT 0,
    total_credit REAL DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(period_id) REFERENCES periods(id),
    FOREIGN KEY(created_by) REFERENCES users(id)
);

-- تفاصيل القيد
CREATE TABLE journal_lines (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    entry_id INTEGER NOT NULL,
    account_id INTEGER NOT NULL,
    debit REAL DEFAULT 0,
    credit REAL DEFAULT 0,
    description TEXT,
    line_number INTEGER NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    FOREIGN KEY(account_id) REFERENCES accounts(id)
);

-- جدول المستخدمين
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL CHECK(role IN ('ADMIN', 'USER', 'VIEWER')),
    full_name TEXT,
    email TEXT,
    is_active INTEGER DEFAULT 1,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- جدول الإعدادات
CREATE TABLE settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_name TEXT,
    company_logo TEXT,
    company_info TEXT,
    company_address TEXT,
    company_phone TEXT,
    company_email TEXT,
    fiscal_year_start TEXT DEFAULT '01-01',
    currency TEXT DEFAULT 'USD',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- جدول أرصدة الحسابات لكل فترة مالية
CREATE TABLE account_balances (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id INTEGER NOT NULL,
    period_id INTEGER NOT NULL,
    opening_balance REAL DEFAULT 0,
    closing_balance REAL DEFAULT 0,
    total_debit REAL DEFAULT 0,
    total_credit REAL DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(account_id) REFERENCES accounts(id),
    FOREIGN KEY(period_id) REFERENCES periods(id),
    UNIQUE(account_id, period_id)
);

-- الفهارس لتحسين الأداء
CREATE INDEX idx_accounts_code ON accounts(code);
CREATE INDEX idx_accounts_parent ON accounts(parent_id);
CREATE INDEX idx_accounts_type ON accounts(account_type);
CREATE INDEX idx_journal_entries_date ON journal_entries(date);
CREATE INDEX idx_journal_entries_period ON journal_entries(period_id);
CREATE INDEX idx_journal_entries_number ON journal_entries(entry_number);
CREATE INDEX idx_journal_lines_entry ON journal_lines(entry_id);
CREATE INDEX idx_journal_lines_account ON journal_lines(account_id);
CREATE INDEX idx_account_balances_account ON account_balances(account_id);
CREATE INDEX idx_account_balances_period ON account_balances(period_id);

-- Triggers لتحديث الأرصدة تلقائياً
CREATE TRIGGER update_journal_entry_totals
AFTER INSERT ON journal_lines
BEGIN
    UPDATE journal_entries
    SET total_debit = (SELECT COALESCE(SUM(debit), 0) FROM journal_lines WHERE entry_id = NEW.entry_id),
        total_credit = (SELECT COALESCE(SUM(credit), 0) FROM journal_lines WHERE entry_id = NEW.entry_id),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.entry_id;
END;

CREATE TRIGGER update_journal_entry_totals_update
AFTER UPDATE ON journal_lines
BEGIN
    UPDATE journal_entries
    SET total_debit = (SELECT COALESCE(SUM(debit), 0) FROM journal_lines WHERE entry_id = NEW.entry_id),
        total_credit = (SELECT COALESCE(SUM(credit), 0) FROM journal_lines WHERE entry_id = NEW.entry_id),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.entry_id;
END;

CREATE TRIGGER update_journal_entry_totals_delete
AFTER DELETE ON journal_lines
BEGIN
    UPDATE journal_entries
    SET total_debit = (SELECT COALESCE(SUM(debit), 0) FROM journal_lines WHERE entry_id = OLD.entry_id),
        total_credit = (SELECT COALESCE(SUM(credit), 0) FROM journal_lines WHERE entry_id = OLD.entry_id),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.entry_id;
END;