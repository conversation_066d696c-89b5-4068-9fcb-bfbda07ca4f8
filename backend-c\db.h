#ifndef DB_H
#define DB_H

#include <sqlite3.h>
#include "models.h"

// دوال قاعدة البيانات الأساسية
int db_open(const char *filename, sqlite3 **db);
void db_close(sqlite3 *db);
int db_init(sqlite3 *db);
int db_load_default_accounts(sqlite3 *db);

// دوال إدارة الحسابات
int db_create_account(sqlite3 *db, const Account *account);
int db_get_account(sqlite3 *db, int id, Account *account);
int db_get_account_by_code(sqlite3 *db, const char *code, Account *account);
int db_update_account(sqlite3 *db, const Account *account);
int db_delete_account(sqlite3 *db, int id);
int db_get_accounts(sqlite3 *db, Account **accounts, int *count);
int db_get_child_accounts(sqlite3 *db, int parent_id, Account **accounts, int *count);

// دوال إدارة الفترات المالية
int db_create_period(sqlite3 *db, const Period *period);
int db_get_period(sqlite3 *db, int id, Period *period);
int db_update_period(sqlite3 *db, const Period *period);
int db_delete_period(sqlite3 *db, int id);
int db_get_periods(sqlite3 *db, Period **periods, int *count);
int db_get_active_period(sqlite3 *db, Period *period);

// دوال إدارة قيود اليومية
int db_create_journal_entry(sqlite3 *db, const JournalEntry *entry);
int db_get_journal_entry(sqlite3 *db, int id, JournalEntry *entry);
int db_update_journal_entry(sqlite3 *db, const JournalEntry *entry);
int db_delete_journal_entry(sqlite3 *db, int id);
int db_get_journal_entries(sqlite3 *db, int period_id, JournalEntry **entries, int *count);
int db_post_journal_entry(sqlite3 *db, int entry_id);

// دوال إدارة تفاصيل القيود
int db_create_journal_line(sqlite3 *db, const JournalLine *line);
int db_get_journal_lines(sqlite3 *db, int entry_id, JournalLine **lines, int *count);
int db_update_journal_line(sqlite3 *db, const JournalLine *line);
int db_delete_journal_line(sqlite3 *db, int id);
int db_delete_journal_lines_by_entry(sqlite3 *db, int entry_id);

// دوال إدارة المستخدمين
int db_create_user(sqlite3 *db, const User *user);
int db_get_user(sqlite3 *db, int id, User *user);
int db_get_user_by_username(sqlite3 *db, const char *username, User *user);
int db_update_user(sqlite3 *db, const User *user);
int db_delete_user(sqlite3 *db, int id);
int db_authenticate_user(sqlite3 *db, const char *username, const char *password, User *user);

// دوال إدارة الإعدادات
int db_get_settings(sqlite3 *db, Settings *settings);
int db_update_settings(sqlite3 *db, const Settings *settings);

// دوال التقارير
int db_get_trial_balance(sqlite3 *db, int period_id, TrialBalanceItem **items, int *count);
int db_get_income_statement(sqlite3 *db, int period_id, IncomeStatementItem **items, int *count);
int db_get_balance_sheet(sqlite3 *db, int period_id, BalanceSheetItem **items, int *count);
int db_get_account_ledger(sqlite3 *db, int account_id, int period_id, JournalLine **lines, int *count);
int db_get_account_balance(sqlite3 *db, int account_id, int period_id, double *balance);

// دوال مساعدة
int db_validate_journal_entry(sqlite3 *db, int entry_id);
int db_calculate_account_balance(sqlite3 *db, int account_id, int period_id, double *balance);
int db_update_account_balances(sqlite3 *db, int period_id);
char* db_generate_entry_number(sqlite3 *db, int period_id);

#endif // DB_H