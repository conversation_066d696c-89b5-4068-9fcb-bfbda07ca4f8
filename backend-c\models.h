#ifndef MODELS_H
#define MODELS_H

// هيكل الحساب
typedef struct {
    int id;
    char code[16];
    char name[128];
    int parent_id;
    double opening_balance;
} Account;

// هيكل الفترة المالية
typedef struct {
    int id;
    char name[32];
    char start_date[16];
    char end_date[16];
    int is_closed;
} Period;

// هيكل المستخدم
typedef struct {
    int id;
    char username[64];
    char password_hash[128];
    char role[16];
} User;

// هيكل قيد اليومية
typedef struct {
    int id;
    char date[16];
    char description[256];
    int period_id;
    int created_by;
} JournalEntry;

// تفاصيل القيد
typedef struct {
    int id;
    int entry_id;
    int account_id;
    double debit;
    double credit;
} JournalLine;

#endif // MODELS_H 