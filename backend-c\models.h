#ifndef MODELS_H
#define MODELS_H

#include <time.h>

// تعريف أنواع الحسابات
typedef enum {
    ACCOUNT_TYPE_ASSET,
    ACCOUNT_TYPE_LIABILITY,
    ACCOUNT_TYPE_EQUITY,
    ACCOUNT_TYPE_REVENUE,
    ACCOUNT_TYPE_EXPENSE
} AccountType;

// تعريف أدوار المستخدمين
typedef enum {
    USER_ROLE_ADMIN,
    USER_ROLE_USER,
    USER_ROLE_VIEWER
} UserRole;

// هيكل الحساب
typedef struct {
    int id;
    char code[16];
    char name[128];
    int parent_id;
    AccountType account_type;
    int is_active;
    double opening_balance;
    char created_at[32];
    char updated_at[32];
} Account;

// هيكل الفترة المالية
typedef struct {
    int id;
    char name[32];
    char start_date[16];
    char end_date[16];
    int is_closed;
    char created_at[32];
    char updated_at[32];
} Period;

// هيكل المستخدم
typedef struct {
    int id;
    char username[64];
    char password_hash[128];
    UserRole role;
    char full_name[128];
    char email[128];
    int is_active;
    char created_at[32];
    char updated_at[32];
} User;

// هيكل قيد اليومية
typedef struct {
    int id;
    char entry_number[32];
    char date[16];
    char description[256];
    int period_id;
    int created_by;
    int is_posted;
    double total_debit;
    double total_credit;
    char created_at[32];
    char updated_at[32];
} JournalEntry;

// تفاصيل القيد
typedef struct {
    int id;
    int entry_id;
    int account_id;
    double debit;
    double credit;
    char description[256];
    int line_number;
    char created_at[32];
} JournalLine;

// هيكل رصيد الحساب
typedef struct {
    int id;
    int account_id;
    int period_id;
    double opening_balance;
    double closing_balance;
    double total_debit;
    double total_credit;
    char created_at[32];
    char updated_at[32];
} AccountBalance;

// هيكل الإعدادات
typedef struct {
    int id;
    char company_name[128];
    char company_logo[256];
    char company_info[512];
    char company_address[256];
    char company_phone[32];
    char company_email[128];
    char fiscal_year_start[8];
    char currency[8];
    char created_at[32];
    char updated_at[32];
} Settings;

// هيكل عنصر ميزان المراجعة
typedef struct {
    int account_id;
    char account_code[16];
    char account_name[128];
    double opening_debit;
    double opening_credit;
    double period_debit;
    double period_credit;
    double closing_debit;
    double closing_credit;
} TrialBalanceItem;

// هيكل عنصر قائمة الدخل
typedef struct {
    int account_id;
    char account_code[16];
    char account_name[128];
    double amount;
    AccountType account_type;
} IncomeStatementItem;

// هيكل عنصر الميزانية العمومية
typedef struct {
    int account_id;
    char account_code[16];
    char account_name[128];
    double amount;
    AccountType account_type;
} BalanceSheetItem;

#endif // MODELS_H