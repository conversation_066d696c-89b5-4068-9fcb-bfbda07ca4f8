#ifndef API_H
#define API_H

#include "db.h"
#include <microhttpd.h>

// دوال السيرفر الأساسية
int start_server(int port);

// دوال معالجة الطلبات
int handle_accounts(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db);
int handle_periods(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db);
int handle_journal_entries(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db);
int handle_journal_lines(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db);
int handle_users(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db);
int handle_settings(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db);
int handle_reports(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db);

// دوال مساعدة
int send_json_response(struct MHD_Connection *connection, int status_code, const char *json);
int send_error_response(struct MHD_Connection *connection, int status_code, const char *message);
char* read_post_data(struct MHD_Connection *connection, const char *upload_data, size_t *upload_data_size);
int parse_json_account(const char *json, Account *account);
int parse_json_journal_entry(const char *json, JournalEntry *entry);
int parse_json_journal_line(const char *json, JournalLine *line);

// دوال تحويل البيانات إلى JSON
char* account_to_json(const Account *account);
char* accounts_to_json(const Account *accounts, int count);
char* journal_entry_to_json(const JournalEntry *entry);
char* journal_entries_to_json(const JournalEntry *entries, int count);
char* journal_line_to_json(const JournalLine *line);
char* journal_lines_to_json(const JournalLine *lines, int count);

#endif // API_H