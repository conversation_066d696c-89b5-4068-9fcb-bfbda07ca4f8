-- إدراج شجرة الحسابات الكاملة حسب المتطلبات المحددة

-- الأصول (1000)
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('1000', 'الأصول', NULL, 'ASSET', 1, 0);

-- الأصول المتداولة (1100)
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('1100', 'الأصول المتداولة', (SELECT id FROM accounts WHERE code = '1000'), 'ASSET', 1, 0);

-- حسابات الأصول المتداولة الفرعية
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('1110', 'النقدية', (SELECT id FROM accounts WHERE code = '1100'), 'ASSET', 1, 0),
('1112', 'البنك', (SELECT id FROM accounts WHERE code = '1100'), 'ASSET', 1, 0),
('1113', 'عهدة مدير', (SELECT id FROM accounts WHERE code = '1100'), 'ASSET', 1, 0),
('1114', 'عهدة موظفين', (SELECT id FROM accounts WHERE code = '1100'), 'ASSET', 1, 0),
('1120', 'العملاء', (SELECT id FROM accounts WHERE code = '1100'), 'ASSET', 1, 0),
('1130', 'المخزون', (SELECT id FROM accounts WHERE code = '1100'), 'ASSET', 1, 0),
('1140', 'سلف موظفين', (SELECT id FROM accounts WHERE code = '1100'), 'ASSET', 1, 0);

-- حسابات المخزون الفرعية
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('1131', 'مخزون المواد الخام', (SELECT id FROM accounts WHERE code = '1130'), 'ASSET', 1, 0),
('1132', 'مخزون الإنتاج التام', (SELECT id FROM accounts WHERE code = '1130'), 'ASSET', 1, 0),
('1133', 'مخزون تحت التشغيل', (SELECT id FROM accounts WHERE code = '1130'), 'ASSET', 1, 0);

-- الأصول الثابتة (1200)
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('1200', 'الأصول الثابتة', (SELECT id FROM accounts WHERE code = '1000'), 'ASSET', 1, 0);

-- حسابات الأصول الثابتة الفرعية
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('1210', 'الأراضي والمباني', (SELECT id FROM accounts WHERE code = '1200'), 'ASSET', 1, 0),
('1220', 'المعدات والآلات', (SELECT id FROM accounts WHERE code = '1200'), 'ASSET', 1, 0),
('1230', 'وسائل النقل', (SELECT id FROM accounts WHERE code = '1200'), 'ASSET', 1, 0);

-- الخصوم (2000)
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('2000', 'الخصوم', NULL, 'LIABILITY', 1, 0);

-- الخصوم المتداولة (2100)
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('2100', 'الخصوم المتداولة', (SELECT id FROM accounts WHERE code = '2000'), 'LIABILITY', 1, 0);

-- حسابات الخصوم المتداولة الفرعية
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('2110', 'الموردين', (SELECT id FROM accounts WHERE code = '2100'), 'LIABILITY', 1, 0),
('2120', 'مصروفات مستحقة', (SELECT id FROM accounts WHERE code = '2100'), 'LIABILITY', 1, 0),
('2130', 'ضرائب مستحقة', (SELECT id FROM accounts WHERE code = '2100'), 'LIABILITY', 1, 0),
('2140', 'أجور مستحقة', (SELECT id FROM accounts WHERE code = '2100'), 'LIABILITY', 1, 0);

-- الخصوم طويلة الأجل (2200)
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('2200', 'الخصوم طويلة الأجل', (SELECT id FROM accounts WHERE code = '2000'), 'LIABILITY', 1, 0);

-- حسابات الخصوم طويلة الأجل الفرعية
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('2210', 'قروض طويلة الأجل', (SELECT id FROM accounts WHERE code = '2200'), 'LIABILITY', 1, 0);

-- حقوق الملكية (3000)
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('3000', 'حقوق الملكية', NULL, 'EQUITY', 1, 0);

-- حسابات حقوق الملكية الفرعية
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('3100', 'رأس المال', (SELECT id FROM accounts WHERE code = '3000'), 'EQUITY', 1, 0),
('3200', 'الأرباح المحتجزة', (SELECT id FROM accounts WHERE code = '3000'), 'EQUITY', 1, 0),
('3300', 'أرباح العام الحالي', (SELECT id FROM accounts WHERE code = '3000'), 'EQUITY', 1, 0);

-- الإيرادات (4000)
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('4000', 'الإيرادات', NULL, 'REVENUE', 1, 0);

-- إيرادات المبيعات (4100)
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('4100', 'إيرادات المبيعات', (SELECT id FROM accounts WHERE code = '4000'), 'REVENUE', 1, 0);

-- حسابات إيرادات المبيعات الفرعية
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('4110', 'مبيعات المنتجات', (SELECT id FROM accounts WHERE code = '4100'), 'REVENUE', 1, 0),
('4120', 'مبيعات الخدمات', (SELECT id FROM accounts WHERE code = '4100'), 'REVENUE', 1, 0),
('4130', 'مردودات المبيعات', (SELECT id FROM accounts WHERE code = '4100'), 'REVENUE', 1, 0);

-- إيرادات أخرى (4200)
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('4200', 'إيرادات أخرى', (SELECT id FROM accounts WHERE code = '4000'), 'REVENUE', 1, 0);

-- المصروفات (5000)
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('5000', 'المصروفات', NULL, 'EXPENSE', 1, 0);

-- تكلفة البضاعة المباعة (5100)
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('5100', 'تكلفة البضاعة المباعة', (SELECT id FROM accounts WHERE code = '5000'), 'EXPENSE', 1, 0);

-- مصروفات التشغيل (5200)
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('5200', 'مصروفات التشغيل', (SELECT id FROM accounts WHERE code = '5000'), 'EXPENSE', 1, 0);

-- حسابات مصروفات التشغيل الفرعية
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('5210', 'مصروفات الرواتب', (SELECT id FROM accounts WHERE code = '5200'), 'EXPENSE', 1, 0),
('5220', 'الإيجارات', (SELECT id FROM accounts WHERE code = '5200'), 'EXPENSE', 1, 0),
('5230', 'الكهرباء والمياه', (SELECT id FROM accounts WHERE code = '5200'), 'EXPENSE', 1, 0),
('5240', 'مصروفات الصيانة', (SELECT id FROM accounts WHERE code = '5200'), 'EXPENSE', 1, 0),
('5250', 'مصروفات المشاريع', (SELECT id FROM accounts WHERE code = '5200'), 'EXPENSE', 1, 0),
('5260', 'مصروفات عامة متنوعة', (SELECT id FROM accounts WHERE code = '5200'), 'EXPENSE', 1, 0),
('5270', 'مصروفات غذائية', (SELECT id FROM accounts WHERE code = '5200'), 'EXPENSE', 1, 0),
('5280', 'مصروفات المطعم', (SELECT id FROM accounts WHERE code = '5200'), 'EXPENSE', 1, 0),
('5290', 'مصروفات التسويق', (SELECT id FROM accounts WHERE code = '5200'), 'EXPENSE', 1, 0);

-- حسابات مصروفات الرواتب الفرعية
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('5211', 'مصروفات المكافأة', (SELECT id FROM accounts WHERE code = '5210'), 'EXPENSE', 1, 0),
('5212', 'مصروفات الوقت الإضافي', (SELECT id FROM accounts WHERE code = '5210'), 'EXPENSE', 1, 0);

-- مصروفات إدارية (5300)
INSERT INTO accounts (code, name, parent_id, account_type, is_active, opening_balance) VALUES 
('5300', 'مصروفات إدارية', (SELECT id FROM accounts WHERE code = '5000'), 'EXPENSE', 1, 0);
