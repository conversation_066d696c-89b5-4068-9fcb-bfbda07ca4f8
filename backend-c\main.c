#include <stdio.h>
#include "db.h"

int main() {
    sqlite3 *db;
    if (db_open("accounting.db", &db) != 0) {
        printf("فشل في فتح قاعدة البيانات\n");
        return 1;
    }
    if (db_init(db) != 0) {
        printf("فشل في تهيئة قاعدة البيانات\n");
        db_close(db);
        return 1;
    }
    printf("تم تهيئة قاعدة البيانات بنجاح\n");
    db_close(db);
    // بدء السيرفر
    extern int start_server(int port);
    start_server(8080);
    return 0;
} 