#include "api.h"
#include <stdio.h>
#include <microhttpd.h>
#include <string.h>
#include <stdlib.h>
#include "db.h"

#define PORT 8080

static sqlite3 *g_db = NULL;

int handle_accounts(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db);

// دالة مساعدة لقراءة جسم الطلب بالكامل
static char *read_post_data(struct MHD_Connection *connection, const char *upload_data, size_t *upload_data_size) {
    if (*upload_data_size == 0) return NULL;
    char *data = malloc(*upload_data_size + 1);
    memcpy(data, upload_data, *upload_data_size);
    data[*upload_data_size] = '\0';
    *upload_data_size = 0;
    return data;
}

static int answer_to_connection(void *cls, struct MHD_Connection *connection,
                                const char *url, const char *method,
                                const char *version, const char *upload_data,
                                size_t *upload_data_size, void **con_cls) {
    if (strncmp(url, "/accounts", 9) == 0) {
        return handle_accounts(connection, url, method, g_db);
    }
    const char *notfound = "404 Not Found";
    return MHD_queue_response(connection, 404, MHD_create_response_from_buffer(strlen(notfound), (void*)notfound, MHD_RESPMEM_PERSISTENT));
}

int start_server(int port) {
    g_db = NULL;
    if (db_open("accounting.db", &g_db) != 0) {
        printf("فشل في فتح قاعدة البيانات\n");
        return 1;
    }
    struct MHD_Daemon *daemon;
    daemon = MHD_start_daemon(MHD_USE_SELECT_INTERNALLY, port, NULL, NULL,
                             &answer_to_connection, NULL, MHD_OPTION_END);
    if (NULL == daemon) return 1;
    printf("REST API يعمل على http://localhost:%d\n", port);
    getchar();
    MHD_stop_daemon(daemon);
    db_close(g_db);
    return 0;
}

// GET /accounts: إرجاع قائمة الحسابات (JSON)
int handle_accounts(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db) {
    if (strcmp(method, "GET") == 0) {
        const char *sql = "SELECT id, code, name, parent_id, opening_balance FROM accounts ORDER BY code";
        sqlite3_stmt *stmt;
        int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, 0);
        if (rc != SQLITE_OK) {
            const char *err = "DB error";
            return MHD_queue_response(connection, 500, MHD_create_response_from_buffer(strlen(err), (void*)err, MHD_RESPMEM_PERSISTENT));
        }
        char *json = malloc(4096); json[0] = '[';
        int first = 1;
        while (sqlite3_step(stmt) == SQLITE_ROW) {
            if (!first) strcat(json, ",");
            else first = 0;
            char buf[512];
            snprintf(buf, sizeof(buf), "{\"id\":%d,\"code\":\"%s\",\"name\":\"%s\",\"parent_id\":%d,\"opening_balance\":%.2f}",
                sqlite3_column_int(stmt,0), sqlite3_column_text(stmt,1), sqlite3_column_text(stmt,2),
                sqlite3_column_int(stmt,3), sqlite3_column_double(stmt,4));
            strcat(json, buf);
        }
        strcat(json, "]");
        sqlite3_finalize(stmt);
        struct MHD_Response *response = MHD_create_response_from_buffer(strlen(json), (void*)json, MHD_RESPMEM_MUST_FREE);
        int ret = MHD_queue_response(connection, 200, response);
        MHD_destroy_response(response);
        return ret;
    }
    if (strcmp(method, "POST") == 0) {
        // إضافة حساب جديد
        const char *code = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "code");
        const char *name = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "name");
        const char *parent_id = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "parent_id");
        const char *opening_balance = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "opening_balance");
        if (!code || !name) {
            const char *err = "code and name required";
            return MHD_queue_response(connection, 400, MHD_create_response_from_buffer(strlen(err), (void*)err, MHD_RESPMEM_PERSISTENT));
        }
        const char *sql = "INSERT INTO accounts (code, name, parent_id, opening_balance) VALUES (?, ?, ?, ?)";
        sqlite3_stmt *stmt;
        int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, 0);
        if (rc != SQLITE_OK) goto fail;
        sqlite3_bind_text(stmt, 1, code, -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 2, name, -1, SQLITE_STATIC);
        if (parent_id) sqlite3_bind_int(stmt, 3, atoi(parent_id));
        else sqlite3_bind_null(stmt, 3);
        if (opening_balance) sqlite3_bind_double(stmt, 4, atof(opening_balance));
        else sqlite3_bind_double(stmt, 4, 0.0);
        rc = sqlite3_step(stmt);
        sqlite3_finalize(stmt);
        if (rc != SQLITE_DONE) goto fail;
        const char *ok = "{\"status\":\"created\"}";
        return MHD_queue_response(connection, 201, MHD_create_response_from_buffer(strlen(ok), (void*)ok, MHD_RESPMEM_PERSISTENT));
    fail:
        {
            const char *err = "DB insert error";
            return MHD_queue_response(connection, 500, MHD_create_response_from_buffer(strlen(err), (void*)err, MHD_RESPMEM_PERSISTENT));
        }
    }
    if (strcmp(method, "PUT") == 0) {
        // تعديل حساب
        // استخراج id من url
        const char *idstr = url + strlen("/accounts/");
        int id = atoi(idstr);
        const char *code = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "code");
        const char *name = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "name");
        const char *parent_id = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "parent_id");
        const char *opening_balance = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "opening_balance");
        if (!code || !name) {
            const char *err = "code and name required";
            return MHD_queue_response(connection, 400, MHD_create_response_from_buffer(strlen(err), (void*)err, MHD_RESPMEM_PERSISTENT));
        }
        const char *sql = "UPDATE accounts SET code=?, name=?, parent_id=?, opening_balance=? WHERE id=?";
        sqlite3_stmt *stmt;
        int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, 0);
        if (rc != SQLITE_OK) goto fail2;
        sqlite3_bind_text(stmt, 1, code, -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 2, name, -1, SQLITE_STATIC);
        if (parent_id) sqlite3_bind_int(stmt, 3, atoi(parent_id));
        else sqlite3_bind_null(stmt, 3);
        if (opening_balance) sqlite3_bind_double(stmt, 4, atof(opening_balance));
        else sqlite3_bind_double(stmt, 4, 0.0);
        sqlite3_bind_int(stmt, 5, id);
        rc = sqlite3_step(stmt);
        sqlite3_finalize(stmt);
        if (rc != SQLITE_DONE) goto fail2;
        const char *ok = "{\"status\":\"updated\"}";
        return MHD_queue_response(connection, 200, MHD_create_response_from_buffer(strlen(ok), (void*)ok, MHD_RESPMEM_PERSISTENT));
    fail2:
        {
            const char *err = "DB update error";
            return MHD_queue_response(connection, 500, MHD_create_response_from_buffer(strlen(err), (void*)err, MHD_RESPMEM_PERSISTENT));
        }
    }
    if (strcmp(method, "DELETE") == 0) {
        // حذف حساب
        const char *idstr = url + strlen("/accounts/");
        int id = atoi(idstr);
        const char *sql = "DELETE FROM accounts WHERE id=?";
        sqlite3_stmt *stmt;
        int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, 0);
        if (rc != SQLITE_OK) goto fail3;
        sqlite3_bind_int(stmt, 1, id);
        rc = sqlite3_step(stmt);
        sqlite3_finalize(stmt);
        if (rc != SQLITE_DONE) goto fail3;
        const char *ok = "{\"status\":\"deleted\"}";
        return MHD_queue_response(connection, 200, MHD_create_response_from_buffer(strlen(ok), (void*)ok, MHD_RESPMEM_PERSISTENT));
    fail3:
        {
            const char *err = "DB delete error";
            return MHD_queue_response(connection, 500, MHD_create_response_from_buffer(strlen(err), (void*)err, MHD_RESPMEM_PERSISTENT));
        }
    }
    const char *notallowed = "Method Not Allowed";
    return MHD_queue_response(connection, 405, MHD_create_response_from_buffer(strlen(notallowed), (void*)notallowed, MHD_RESPMEM_PERSISTENT));
} 