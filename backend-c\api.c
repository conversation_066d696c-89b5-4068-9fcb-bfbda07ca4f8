#include "api.h"
#include <stdio.h>
#include <microhttpd.h>
#include <string.h>
#include <stdlib.h>
#include "db.h"

#define PORT 8080

static sqlite3 *g_db = NULL;

int handle_accounts(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db);

// دوال مساعدة
int send_json_response(struct MHD_Connection *connection, int status_code, const char *json) {
    struct MHD_Response *response = MHD_create_response_from_buffer(strlen(json), (void*)json, MHD_RESPMEM_MUST_COPY);
    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    MHD_add_response_header(response, "Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    MHD_add_response_header(response, "Access-Control-Allow-Headers", "Content-Type, Authorization");
    int ret = MHD_queue_response(connection, status_code, response);
    MHD_destroy_response(response);
    return ret;
}

int send_error_response(struct MHD_Connection *connection, int status_code, const char *message) {
    char json[512];
    snprintf(json, sizeof(json), "{\"error\":\"%s\",\"status\":%d}", message, status_code);
    return send_json_response(connection, status_code, json);
}

char* read_post_data(struct MHD_Connection *connection, const char *upload_data, size_t *upload_data_size) {
    if (*upload_data_size == 0) return NULL;
    char *data = malloc(*upload_data_size + 1);
    memcpy(data, upload_data, *upload_data_size);
    data[*upload_data_size] = '\0';
    *upload_data_size = 0;
    return data;
}

char* account_to_json(const Account *account) {
    char *json = malloc(1024);
    snprintf(json, 1024,
        "{\"id\":%d,\"code\":\"%s\",\"name\":\"%s\",\"parent_id\":%d,\"account_type\":\"%s\",\"is_active\":%d,\"opening_balance\":%.2f,\"created_at\":\"%s\",\"updated_at\":\"%s\"}",
        account->id, account->code, account->name, account->parent_id,
        account_type_to_string(account->account_type), account->is_active, account->opening_balance,
        account->created_at, account->updated_at);
    return json;
}

char* accounts_to_json(const Account *accounts, int count) {
    char *json = malloc(count * 1024 + 100);
    strcpy(json, "[");
    for (int i = 0; i < count; i++) {
        if (i > 0) strcat(json, ",");
        char *account_json = account_to_json(&accounts[i]);
        strcat(json, account_json);
        free(account_json);
    }
    strcat(json, "]");
    return json;
}

char* journal_entry_to_json(const JournalEntry *entry) {
    char *json = malloc(1024);
    snprintf(json, 1024,
        "{\"id\":%d,\"entry_number\":\"%s\",\"date\":\"%s\",\"description\":\"%s\",\"period_id\":%d,\"created_by\":%d,\"is_posted\":%d,\"total_debit\":%.2f,\"total_credit\":%.2f,\"created_at\":\"%s\",\"updated_at\":\"%s\"}",
        entry->id, entry->entry_number, entry->date, entry->description, entry->period_id,
        entry->created_by, entry->is_posted, entry->total_debit, entry->total_credit,
        entry->created_at, entry->updated_at);
    return json;
}

char* journal_entries_to_json(const JournalEntry *entries, int count) {
    char *json = malloc(count * 1024 + 100);
    strcpy(json, "[");
    for (int i = 0; i < count; i++) {
        if (i > 0) strcat(json, ",");
        char *entry_json = journal_entry_to_json(&entries[i]);
        strcat(json, entry_json);
        free(entry_json);
    }
    strcat(json, "]");
    return json;
}

char* journal_line_to_json(const JournalLine *line) {
    char *json = malloc(512);
    snprintf(json, 512,
        "{\"id\":%d,\"entry_id\":%d,\"account_id\":%d,\"debit\":%.2f,\"credit\":%.2f,\"description\":\"%s\",\"line_number\":%d,\"created_at\":\"%s\"}",
        line->id, line->entry_id, line->account_id, line->debit, line->credit,
        line->description, line->line_number, line->created_at);
    return json;
}

char* journal_lines_to_json(const JournalLine *lines, int count) {
    char *json = malloc(count * 512 + 100);
    strcpy(json, "[");
    for (int i = 0; i < count; i++) {
        if (i > 0) strcat(json, ",");
        char *line_json = journal_line_to_json(&lines[i]);
        strcat(json, line_json);
        free(line_json);
    }
    strcat(json, "]");
    return json;
}

static int answer_to_connection(void *cls, struct MHD_Connection *connection,
                                const char *url, const char *method,
                                const char *version, const char *upload_data,
                                size_t *upload_data_size, void **con_cls) {

    // إضافة CORS headers
    struct MHD_Response *response;
    int ret;

    if (strncmp(url, "/accounts", 9) == 0) {
        return handle_accounts(connection, url, method, g_db);
    }
    else if (strncmp(url, "/periods", 8) == 0) {
        return handle_periods(connection, url, method, g_db);
    }
    else if (strncmp(url, "/journal-entries", 16) == 0) {
        return handle_journal_entries(connection, url, method, g_db);
    }
    else if (strncmp(url, "/journal-lines", 14) == 0) {
        return handle_journal_lines(connection, url, method, g_db);
    }
    else if (strncmp(url, "/users", 6) == 0) {
        return handle_users(connection, url, method, g_db);
    }
    else if (strncmp(url, "/settings", 9) == 0) {
        return handle_settings(connection, url, method, g_db);
    }
    else if (strncmp(url, "/reports", 8) == 0) {
        return handle_reports(connection, url, method, g_db);
    }

    return send_error_response(connection, 404, "Endpoint not found");
}

int start_server(int port) {
    g_db = NULL;
    if (db_open("accounting.db", &g_db) != 0) {
        printf("فشل في فتح قاعدة البيانات\n");
        return 1;
    }
    struct MHD_Daemon *daemon;
    daemon = MHD_start_daemon(MHD_USE_SELECT_INTERNALLY, port, NULL, NULL,
                             &answer_to_connection, NULL, MHD_OPTION_END);
    if (NULL == daemon) return 1;
    printf("REST API يعمل على http://localhost:%d\n", port);
    getchar();
    MHD_stop_daemon(daemon);
    db_close(g_db);
    return 0;
}

// GET /accounts: إرجاع قائمة الحسابات (JSON)
int handle_accounts(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db) {
    if (strcmp(method, "GET") == 0) {
        const char *sql = "SELECT id, code, name, parent_id, opening_balance FROM accounts ORDER BY code";
        sqlite3_stmt *stmt;
        int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, 0);
        if (rc != SQLITE_OK) {
            const char *err = "DB error";
            return MHD_queue_response(connection, 500, MHD_create_response_from_buffer(strlen(err), (void*)err, MHD_RESPMEM_PERSISTENT));
        }
        char *json = malloc(4096); json[0] = '[';
        int first = 1;
        while (sqlite3_step(stmt) == SQLITE_ROW) {
            if (!first) strcat(json, ",");
            else first = 0;
            char buf[512];
            snprintf(buf, sizeof(buf), "{\"id\":%d,\"code\":\"%s\",\"name\":\"%s\",\"parent_id\":%d,\"opening_balance\":%.2f}",
                sqlite3_column_int(stmt,0), sqlite3_column_text(stmt,1), sqlite3_column_text(stmt,2),
                sqlite3_column_int(stmt,3), sqlite3_column_double(stmt,4));
            strcat(json, buf);
        }
        strcat(json, "]");
        sqlite3_finalize(stmt);
        struct MHD_Response *response = MHD_create_response_from_buffer(strlen(json), (void*)json, MHD_RESPMEM_MUST_FREE);
        int ret = MHD_queue_response(connection, 200, response);
        MHD_destroy_response(response);
        return ret;
    }
    if (strcmp(method, "POST") == 0) {
        // إضافة حساب جديد
        const char *code = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "code");
        const char *name = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "name");
        const char *parent_id = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "parent_id");
        const char *opening_balance = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "opening_balance");
        if (!code || !name) {
            const char *err = "code and name required";
            return MHD_queue_response(connection, 400, MHD_create_response_from_buffer(strlen(err), (void*)err, MHD_RESPMEM_PERSISTENT));
        }
        const char *sql = "INSERT INTO accounts (code, name, parent_id, opening_balance) VALUES (?, ?, ?, ?)";
        sqlite3_stmt *stmt;
        int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, 0);
        if (rc != SQLITE_OK) goto fail;
        sqlite3_bind_text(stmt, 1, code, -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 2, name, -1, SQLITE_STATIC);
        if (parent_id) sqlite3_bind_int(stmt, 3, atoi(parent_id));
        else sqlite3_bind_null(stmt, 3);
        if (opening_balance) sqlite3_bind_double(stmt, 4, atof(opening_balance));
        else sqlite3_bind_double(stmt, 4, 0.0);
        rc = sqlite3_step(stmt);
        sqlite3_finalize(stmt);
        if (rc != SQLITE_DONE) goto fail;
        const char *ok = "{\"status\":\"created\"}";
        return MHD_queue_response(connection, 201, MHD_create_response_from_buffer(strlen(ok), (void*)ok, MHD_RESPMEM_PERSISTENT));
    fail:
        {
            const char *err = "DB insert error";
            return MHD_queue_response(connection, 500, MHD_create_response_from_buffer(strlen(err), (void*)err, MHD_RESPMEM_PERSISTENT));
        }
    }
    if (strcmp(method, "PUT") == 0) {
        // تعديل حساب
        // استخراج id من url
        const char *idstr = url + strlen("/accounts/");
        int id = atoi(idstr);
        const char *code = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "code");
        const char *name = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "name");
        const char *parent_id = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "parent_id");
        const char *opening_balance = MHD_lookup_connection_value(connection, MHD_POSTDATA_KIND, "opening_balance");
        if (!code || !name) {
            const char *err = "code and name required";
            return MHD_queue_response(connection, 400, MHD_create_response_from_buffer(strlen(err), (void*)err, MHD_RESPMEM_PERSISTENT));
        }
        const char *sql = "UPDATE accounts SET code=?, name=?, parent_id=?, opening_balance=? WHERE id=?";
        sqlite3_stmt *stmt;
        int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, 0);
        if (rc != SQLITE_OK) goto fail2;
        sqlite3_bind_text(stmt, 1, code, -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 2, name, -1, SQLITE_STATIC);
        if (parent_id) sqlite3_bind_int(stmt, 3, atoi(parent_id));
        else sqlite3_bind_null(stmt, 3);
        if (opening_balance) sqlite3_bind_double(stmt, 4, atof(opening_balance));
        else sqlite3_bind_double(stmt, 4, 0.0);
        sqlite3_bind_int(stmt, 5, id);
        rc = sqlite3_step(stmt);
        sqlite3_finalize(stmt);
        if (rc != SQLITE_DONE) goto fail2;
        const char *ok = "{\"status\":\"updated\"}";
        return MHD_queue_response(connection, 200, MHD_create_response_from_buffer(strlen(ok), (void*)ok, MHD_RESPMEM_PERSISTENT));
    fail2:
        {
            const char *err = "DB update error";
            return MHD_queue_response(connection, 500, MHD_create_response_from_buffer(strlen(err), (void*)err, MHD_RESPMEM_PERSISTENT));
        }
    }
    if (strcmp(method, "DELETE") == 0) {
        // حذف حساب
        const char *idstr = url + strlen("/accounts/");
        int id = atoi(idstr);
        const char *sql = "DELETE FROM accounts WHERE id=?";
        sqlite3_stmt *stmt;
        int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, 0);
        if (rc != SQLITE_OK) goto fail3;
        sqlite3_bind_int(stmt, 1, id);
        rc = sqlite3_step(stmt);
        sqlite3_finalize(stmt);
        if (rc != SQLITE_DONE) goto fail3;
        const char *ok = "{\"status\":\"deleted\"}";
        return MHD_queue_response(connection, 200, MHD_create_response_from_buffer(strlen(ok), (void*)ok, MHD_RESPMEM_PERSISTENT));
    fail3:
        {
            const char *err = "DB delete error";
            return MHD_queue_response(connection, 500, MHD_create_response_from_buffer(strlen(err), (void*)err, MHD_RESPMEM_PERSISTENT));
        }
    }
    return send_error_response(connection, 405, "Method Not Allowed");
}

// معالج قيود اليومية
int handle_journal_entries(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db) {
    if (strcmp(method, "GET") == 0) {
        // GET /journal-entries أو GET /journal-entries/{id}
        if (strlen(url) > 16) {
            // طلب قيد محدد
            int id = atoi(url + 17);
            JournalEntry entry;
            int rc = db_get_journal_entry(db, id, &entry);
            if (rc == SQLITE_OK) {
                char *json = journal_entry_to_json(&entry);
                int ret = send_json_response(connection, 200, json);
                free(json);
                return ret;
            } else {
                return send_error_response(connection, 404, "Journal entry not found");
            }
        } else {
            // طلب جميع القيود
            const char *period_id_str = MHD_lookup_connection_value(connection, MHD_GET_ARGUMENT_KIND, "period_id");
            int period_id = period_id_str ? atoi(period_id_str) : 0;

            JournalEntry *entries;
            int count;
            int rc = db_get_journal_entries(db, period_id, &entries, &count);
            if (rc == SQLITE_OK) {
                char *json = journal_entries_to_json(entries, count);
                int ret = send_json_response(connection, 200, json);
                free(json);
                free(entries);
                return ret;
            } else {
                return send_error_response(connection, 500, "Database error");
            }
        }
    }
    else if (strcmp(method, "POST") == 0) {
        // إنشاء قيد جديد
        // TODO: قراءة البيانات من JSON وإنشاء القيد
        return send_error_response(connection, 501, "Not implemented yet");
    }
    else if (strcmp(method, "PUT") == 0) {
        // تعديل قيد موجود
        // TODO: قراءة البيانات من JSON وتعديل القيد
        return send_error_response(connection, 501, "Not implemented yet");
    }
    else if (strcmp(method, "DELETE") == 0) {
        // حذف قيد
        int id = atoi(url + 17);
        int rc = db_delete_journal_entry(db, id);
        if (rc == SQLITE_OK) {
            return send_json_response(connection, 200, "{\"status\":\"deleted\"}");
        } else {
            return send_error_response(connection, 500, "Failed to delete journal entry");
        }
    }

    return send_error_response(connection, 405, "Method Not Allowed");
}

// معالج تفاصيل القيود
int handle_journal_lines(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db) {
    if (strcmp(method, "GET") == 0) {
        // GET /journal-lines?entry_id={id}
        const char *entry_id_str = MHD_lookup_connection_value(connection, MHD_GET_ARGUMENT_KIND, "entry_id");
        if (!entry_id_str) {
            return send_error_response(connection, 400, "entry_id parameter required");
        }

        int entry_id = atoi(entry_id_str);
        JournalLine *lines;
        int count;
        int rc = db_get_journal_lines(db, entry_id, &lines, &count);
        if (rc == SQLITE_OK) {
            char *json = journal_lines_to_json(lines, count);
            int ret = send_json_response(connection, 200, json);
            free(json);
            free(lines);
            return ret;
        } else {
            return send_error_response(connection, 500, "Database error");
        }
    }

    return send_error_response(connection, 405, "Method Not Allowed");
}

// معالج الفترات المالية
int handle_periods(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db) {
    // TODO: تنفيذ معالج الفترات المالية
    return send_error_response(connection, 501, "Not implemented yet");
}

// معالج المستخدمين
int handle_users(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db) {
    // TODO: تنفيذ معالج المستخدمين
    return send_error_response(connection, 501, "Not implemented yet");
}

// معالج الإعدادات
int handle_settings(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db) {
    // TODO: تنفيذ معالج الإعدادات
    return send_error_response(connection, 501, "Not implemented yet");
}

// معالج التقارير
int handle_reports(struct MHD_Connection *connection, const char *url, const char *method, sqlite3 *db) {
    // TODO: تنفيذ معالج التقارير
    return send_error_response(connection, 501, "Not implemented yet");
}